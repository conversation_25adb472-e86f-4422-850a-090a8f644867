=== RUN   TestNewMissingHost
--- PASS: TestNewMissingHost (0.00s)
=== RUN   TestNewMissingAppID
--- PASS: TestNewMissingAppID (0.00s)
=== RUN   TestNewMissingSecret
--- PASS: TestNewMissingSecret (0.00s)
=== RUN   TestGetBasicAuthToken
--- PASS: TestGetBasicAuthToken (0.00s)
=== RUN   TestGetWeatherByLocationSuccess
--- PASS: TestGetWeatherByLocationSuccess (0.00s)
=== RUN   TestGetWeatherByLocationStatusCodeError
--- PASS: TestGetWeatherByLocationStatusCodeError (0.00s)
=== RUN   TestGetWeatherByLocationBizError
--- PASS: TestGetWeatherByLocationBizError (0.00s)
PASS
ok  	ccc-gitlab.leihuo.netease.com/cccgo/sdk_weather	0.006s
