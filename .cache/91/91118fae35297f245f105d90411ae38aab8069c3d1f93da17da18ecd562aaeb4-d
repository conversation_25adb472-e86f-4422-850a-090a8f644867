=== RUN   TestNewMissingHost
--- PASS: TestNewMissingHost (0.00s)
=== RUN   TestNewMissingAppID
--- PASS: TestNewMissingAppID (0.00s)
=== RUN   TestNewMissingSecret
--- PASS: TestNewMissingSecret (0.00s)
=== RUN   TestGetBasicAuthToken
--- PASS: TestGetBasicAuthToken (0.00s)
=== RUN   TestNewDefaultClientAndLogger
--- PASS: TestNewDefaultClientAndLogger (0.00s)
=== RUN   TestGetWeatherByLocationSuccess
--- PASS: TestGetWeatherByLocationSuccess (0.00s)
=== RUN   TestGetWeatherByLocationStatusCodeError
time="2025-09-19T15:38:26+08:00" level=warning msg="sdk_weather: 天气接口返回非200状态码" body= status_code=500 url="http://example.com/xyq_service/admin/xyq-lbs/v1/weather?gameid=game&lat=0.000000&log=0.000000"
--- PASS: TestGetWeatherByLocationStatusCodeError (0.00s)
=== RUN   TestGetWeatherByLocationBizError
time="2025-09-19T15:38:26+08:00" level=warning msg="sdk_weather: 天气接口返回业务错误" biz_code=1001 body="{\"code\": 1001}" url="http://example.com/xyq_service/admin/xyq-lbs/v1/weather?gameid=game&lat=0.000000&log=0.000000"
--- PASS: TestGetWeatherByLocationBizError (0.00s)
PASS
ok  	ccc-gitlab.leihuo.netease.com/cccgo/sdk_weather	0.004s
